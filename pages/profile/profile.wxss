/* pages/profile/profile.wxss */
.profile-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
  background-color: #f8f8f8;
  margin: 20rpx;
  border-radius: 20rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-bottom: 20rpx;
}

.nickname {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.no-user {
  color: #999;
  font-size: 28rpx;
}

.menu-list {
  margin: 40rpx 20rpx;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 20rpx;
  background-color: white;
  border-bottom: 1rpx solid #eee;
  font-size: 30rpx;
}

.menu-item:first-child {
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
}

.menu-item:last-child {
  border-bottom-left-radius: 20rpx;
  border-bottom-right-radius: 20rpx;
  border-bottom: none;
}

.arrow {
  color: #ccc;
  font-size: 24rpx;
}