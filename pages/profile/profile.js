// pages/profile/profile.js
Page({
  data: {
    userInfo: {},
    hasUserInfo: false
  },

  onLoad() {
    const app = getApp()
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo,
        hasUserInfo: true
      })
    }
  },

  onShow() {
    // 页面显示时更新用户信息
    const app = getApp()
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo,
        hasUserInfo: true
      })
    }
  },

  onSettingsTap() {
    wx.showToast({
      title: '设置功能开发中',
      icon: 'none'
    })
  },

  onAboutTap() {
    wx.showModal({
      title: '关于我们',
      content: '这是一个微信小程序示例项目',
      showCancel: false
    })
  }
})