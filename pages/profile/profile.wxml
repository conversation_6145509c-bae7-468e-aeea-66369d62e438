<!--pages/profile/profile.wxml-->
<view class="container">
  <view class="title">个人中心</view>
  
  <view class="profile-section" wx:if="{{hasUserInfo}}">
    <image class="avatar" src="{{userInfo.avatarUrl}}" mode="cover"></image>
    <text class="nickname">{{userInfo.nickName}}</text>
  </view>
  
  <view class="profile-section" wx:else>
    <text class="no-user">请先在首页获取用户信息</text>
  </view>
  
  <view class="menu-list">
    <view class="menu-item" bindtap="onSettingsTap">
      <text>设置</text>
      <text class="arrow">></text>
    </view>
    <view class="menu-item" bindtap="onAboutTap">
      <text>关于我们</text>
      <text class="arrow">></text>
    </view>
  </view>
</view>