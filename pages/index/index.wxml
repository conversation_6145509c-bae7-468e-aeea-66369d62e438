<!--pages/index/index.wxml-->
<view class="container">
  <view class="title">欢迎使用微信小程序</view>
  
  <view class="userinfo">
    <button wx:if="{{!hasUserInfo && canIUse}}" open-type="getUserInfo" bindgetuserinfo="getUserInfo" class="btn">获取头像昵称</button>
    <block wx:else>
      <image bindtap="bindViewTap" class="userinfo-avatar" src="{{userInfo.avatarUrl}}" mode="cover"></image>
      <text class="userinfo-nickname">{{userInfo.nickName}}</text>
    </block>
  </view>
  
  <view class="usermotto">
    <text class="user-motto">{{motto}}</text>
  </view>
  
  <button class="btn" bindtap="onButtonTap">点击测试</button>
</view>